# Seed Data Documentation

## Overview

This document describes the comprehensive seed data created for testing the MyBinder group management system. The seed data includes users with different roles, groups with various privacy settings, and realistic content to test all implemented features.

## Database Reset and Seeding

To reset and seed the database with fresh test data:

```bash
npx tsx prisma/seed.ts
```

## Demo Accounts

### 👑 Owner Accounts

#### <PERSON> (Owner 1)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `owner1`
- **Groups Owned**:
  - General Discussion (Public)
  - Project Planning (Public)
  - Leadership Team (Private)

#### <PERSON> (Owner 2)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `owner2`
- **Groups Owned**:
  - Tech Discussions (Public)
  - VIP Members (Private)

### 🛡️ Admin Accounts

#### <PERSON> (Admin 1)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `admin1`
- **Admin Role In**:
  - General Discussion
  - Project Planning
  - Leadership Team

#### <PERSON> (Admin 2)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `admin2`
- **Admin Role In**:
  - General Discussion
  - Tech Discussions
  - VIP Members

### 👥 Member Accounts

#### Lisa Thompson (Member 1)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `member1`
- **Member In**:
  - General Discussion
  - Project Planning
  - VIP Members
- **Owner Of**: Small Team (for testing ownership transfer)

#### James Wilson (Member 2)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `member2`
- **Member In**:
  - General Discussion
  - Tech Discussions
  - Small Team (now owner after transfer)

#### Anna Garcia (Member 3)
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `member3`
- **Member In**:
  - Project Planning
  - Tech Discussions

### 🧪 Test Account

#### Test User
- **Email**: `<EMAIL>`
- **Password**: `demo123`
- **Username**: `testuser`
- **Purpose**: For testing join/leave functionality
- **Initial State**: Not in any groups

## Groups Structure

### 🌐 Public Groups

#### 1. General Discussion
- **Owner**: Sarah Johnson (owner1)
- **Privacy**: Public
- **Members**: 6 total
  - 1 Owner: Sarah Johnson
  - 2 Admins: Emma Rodriguez, David Kim
  - 3 Members: Lisa Thompson, James Wilson, Test User (promoted to Admin)
- **Content**: 5 messages, 1 note

#### 2. Project Planning
- **Owner**: Sarah Johnson (owner1)
- **Privacy**: Public
- **Members**: 4 total
  - 1 Owner: Sarah Johnson
  - 1 Admin: Emma Rodriguez
  - 2 Members: Lisa Thompson, Anna Garcia
- **Content**: 4 messages, 1 note

#### 3. Tech Discussions
- **Owner**: Michael Chen (owner2)
- **Privacy**: Public
- **Members**: 4 total
  - 1 Owner: Michael Chen
  - 1 Admin: David Kim
  - 2 Members: James Wilson, Anna Garcia
- **Content**: 3 messages, 1 note

#### 4. Small Team
- **Owner**: James Wilson (member2) - *transferred from Lisa Thompson*
- **Privacy**: Public
- **Members**: 2 total
  - 1 Owner: James Wilson
  - 1 Member: Lisa Thompson (former owner)
- **Content**: 1 message, 1 note
- **Purpose**: Testing ownership transfer functionality

### 🔒 Private Groups

#### 1. Leadership Team
- **Owner**: Sarah Johnson (owner1)
- **Privacy**: Private
- **Members**: 3 total
  - 1 Owner: Sarah Johnson
  - 2 Admins: Michael Chen, Emma Rodriguez
- **Content**: 2 messages, 1 note

#### 2. VIP Members
- **Owner**: Michael Chen (owner2)
- **Privacy**: Private
- **Members**: 3 total
  - 1 Owner: Michael Chen
  - 1 Admin: David Kim
  - 1 Member: Lisa Thompson
- **Content**: 0 messages, 0 notes

## Testing Scenarios

### ✅ Role-Based Permissions Testing

1. **Owner Permissions**:
   - Login as `<EMAIL>` or `<EMAIL>`
   - Test: Delete groups, transfer ownership, promote/demote any member

2. **Admin Permissions**:
   - Login as `<EMAIL>` or `<EMAIL>`
   - Test: Manage members (promote/demote members only), cannot delete groups

3. **Member Permissions**:
   - Login as `<EMAIL>`, `<EMAIL>`, or `<EMAIL>`
   - Test: Leave groups, view content, limited management capabilities

### ✅ Join/Leave Group Testing

1. **Join Public Groups**:
   - Login as `<EMAIL>`
   - Use Discover Groups feature to find and join public groups
   - Verify private groups are not visible in discovery

2. **Leave Groups**:
   - Use any member account to test leaving groups
   - Test owner leaving with members (should be prevented)

### ✅ Transfer Ownership Testing

1. **Small Team Group**:
   - Originally owned by Lisa Thompson (member1)
   - Already transferred to James Wilson (member2)
   - Can test reverse transfer or to other members

### ✅ Role Management Testing

1. **Promote/Demote Members**:
   - Login as owner accounts to promote members to admin
   - Test admin accounts trying to promote (should fail for admin promotion)
   - Test demoting admins back to members

2. **Remove Members**:
   - Test removing members with appropriate permissions
   - Verify permission matrix enforcement

### ✅ Group Discovery Testing

1. **Public Group Discovery**:
   - Login as `<EMAIL>`
   - Use Discover Groups to see all public groups
   - Join groups and verify they disappear from discovery

2. **Private Group Isolation**:
   - Verify private groups don't appear in discovery
   - Test that only invited members can access private groups

## Content Overview

### Messages
- **Total**: 15 messages across all groups
- **Distribution**: Realistic conversations in each group
- **Authors**: Messages from various roles to demonstrate interaction

### Notes
- **Total**: 5 notes with comprehensive block content
- **Types**: Meeting notes, brainstorming, technical documentation
- **Blocks**: Various block types (headings, text, lists) for editor testing

### Note Blocks
- **Total**: 25+ blocks across all notes
- **Types**: HEADING_1, HEADING_2, TEXT, BULLET_LIST, NUMBERED_LIST
- **Purpose**: Testing the block-based note editor functionality

## Database Schema Alignment

The seed data is fully aligned with the current Prisma schema:

- ✅ **Role Enum**: Uses OWNER, ADMIN, MEMBER values
- ✅ **Foreign Keys**: All relationships properly maintained
- ✅ **Privacy Settings**: Both public and private groups included
- ✅ **Cascade Deletion**: Proper parent-child relationships
- ✅ **User Authentication**: Hashed passwords using bcrypt

## Quick Start Testing Guide

1. **Reset Database**: `npx tsx prisma/seed.ts`
2. **Start Server**: `npm run dev`
3. **Open Browser**: `http://localhost:3001`
4. **Login Options**:
   - Owner: `<EMAIL>` / `demo123`
   - Admin: `<EMAIL>` / `demo123`
   - Member: `<EMAIL>` / `demo123`
   - Test User: `<EMAIL>` / `demo123`

## API Testing Examples

```bash
# Login as test user
curl -X POST http://localhost:3001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "demo123"}' \
  -c cookies.txt

# Discover public groups
curl -X GET http://localhost:3001/api/groups/discover -b cookies.txt

# Join a public group
curl -X POST http://localhost:3001/api/groups/{groupId}/join -b cookies.txt

# Leave a group
curl -X POST http://localhost:3001/api/groups/{groupId}/leave -b cookies.txt
```

This comprehensive seed data provides a realistic testing environment for all group management features while maintaining data consistency and demonstrating the full range of role-based permissions.
